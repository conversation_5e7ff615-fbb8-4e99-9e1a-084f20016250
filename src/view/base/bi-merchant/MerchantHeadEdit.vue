<template>
  <section  >
<!--    <div class="tag-card">
      客户基础信息
    </div>
    <div class="cs-divider"></div>-->
    <a-card size="small" title="客商信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <a-form-item name="merchantCode" :label="'客商编码'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.merchantCode"/>
          </a-form-item>
          <a-form-item name="financeCode" :label="'财务系统编码'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.financeCode"/>
          </a-form-item>

          <a-form-item name="merchantNameCn" :label="'客商中文名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.merchantNameCn"/>
          </a-form-item>

          <a-form-item name="merchantNameEn" :label="'客商英文名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.merchantNameEn"/>
          </a-form-item>

          <!-- 客商类别 -->
          <a-form-item name="merchantType" :label="'客商类别'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.merchantType" id="merchantType">
              <a-select-option v-for="item in productClassify.merchantType"  :key="item.label  " :value="item.value" :label="item.label">
                {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!-- 常用标识 -->
          <a-form-item name="commonFlag" :label="'常用标识'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.commonFlagList"
              :disabled="showDisable"
              mode="multiple"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="productClassify.businessType"
            ></a-select>
          </a-form-item>

          <!-- 传真 -->
          <a-form-item name="fax" :label="'传真'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.fax"/>
          </a-form-item>

          <!-- 邮件 -->
          <a-form-item name="email" :label="'邮件'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.email"/>
          </a-form-item>

          <!-- 英文地址 -->
          <a-form-item name="enAddress" :label="'英文地址'" class="grid-item merge-3" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.enAddress"/>
          </a-form-item>

          <!-- 贸易国别英文 -->
          <a-form-item name="tradeCountryEn" :label="'贸易国别英文'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.tradeCountryEn"/>
          </a-form-item>

          <!-- 邮编 -->
          <a-form-item name="postcode" :label="'邮编'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.postcode"/>
          </a-form-item>

          <!-- 开户行地址 -->
          <a-form-item name="bankAddress" :label="'开户行地址'" class="grid-item merge-3" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.bankAddress"/>
          </a-form-item>

          <!-- 支出账号 -->
          <a-form-item name="expendAccount" :label="'支出账号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.expendAccount"/>
          </a-form-item>

          <!-- 税号 -->
          <a-form-item name="taxNo" :label="'税号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.taxNo"/>
          </a-form-item>

          <!-- 法人代表 -->
          <a-form-item name="legalPerson" :label="'法人代表'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.legalPerson"/>
          </a-form-item>

          <!-- 客商简称 -->
<!--          <a-form-item name="merchantShort" :label="'客商简称'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.merchantShort"/>-->
<!--          </a-form-item>-->

          <!-- 收款银行 -->
          <a-form-item name="receivingBank" :label="'收款银行'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.receivingBank"/>
          </a-form-item>

          <!-- 收款方帐号 -->
          <a-form-item name="receiverAccountNum" :label="'收款方帐号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.receiverAccountNum"/>
          </a-form-item>
          <!-- 客商地址 -->
          <a-form-item name="merchantAddress" :label="'客商地址'" class="grid-item merge-3" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.merchantAddress"/>
          </a-form-item>
          <!-- 备注 -->
          <a-form-item name="note" :label="'备注'" class="grid-item merge-3" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"/>
          </a-form-item>

          <!-- 贸易国别 -->
          <a-form-item name="tradeCountry" :label="'贸易国别'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.tradeCountry"/>
          </a-form-item>

          <!-- 联系人 -->
          <a-form-item name="contactPerson" :label="'联系人'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.contactPerson"/>
          </a-form-item>

          <!-- 联系电话 -->
          <a-form-item name="contactPhone" :label="'联系电话'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.contactPhone"/>
          </a-form-item>

          <!-- 装运人SHIPPER -->
          <a-form-item name="shipper" :label="'装运人SHIPPER'" class="grid-item merge-3" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.shipper"/>
          </a-form-item>

          <!-- 收货人CONSIGNEE -->
          <a-form-item name="consignee" :label="'收货人CONSIGNEE'" class="grid-item merge-3" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.consignee"/>
          </a-form-item>

          <!-- 通知人NOTIFY PARTY -->
          <a-form-item name="notifyParty" :label="'通知人NOTIFY PARTY'" class="grid-item merge-3" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.notifyParty"/>
          </a-form-item>

          <!-- 仓储地址 -->
          <a-form-item name="warehouseAddress" :label="'仓储地址'" class="grid-item merge-3" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.warehouseAddress"/>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(false)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import {usePCode} from "@/view/common/usePCode";
import {MerchantInsertClient, MerchantUpdateClient} from "@/api/bi/bi_client_info";
import CsSelect from "@/components/select/CsSelect.vue";
const { getPCode } = usePCode()



const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onBack']);

const onBack = (val) => {
  emit('onBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  sid:'',
  merchantCode: '',
  merchantNameCn: '',
  merchantNameEn: '',
  merchantType: '',
  commonFlagList: [],
  commonFlag: '',
  fax: '',
  email: '',
  enAddress: '',
  tradeCountryEn: '',
  postcode: '',
  bankAddress: '',
  expendAccount: '',
  taxNo: '',
  legalPerson: '',
  financeCode: '',
  merchantAddress: '',
  receivingBank: '',
  receiverAccountNum: '',
  note: '',
  tradeCountry: '',
  shipper: '',
  consignee: '',
  notifyParty: '',
  warehouseAddress: '',
  contactPerson: '',
  contactPhone: ''
})
// 校验规则
const rules = {

  merchantCode: [
    // { required: true, message: '客商中文名称不能为空！', trigger: 'blur' },
    {max: 30, message: '客商编码长度不能超过30位字节', trigger: 'blur'}
  ],
  merchantNameCn: [
    { required: true, message: '客商中文名称不能为空！', trigger: 'blur' },
    {max: 100, message: '客商中文名称长度不能超过100位字节', trigger: 'blur'}
  ],
  merchantNameEn: [
    {max: 200, message: '客商英文名称长度不能超过200位字节', trigger: 'blur'}
  ],
  financeCode: [
    {max: 30, message: '财务系统编码不能超过30位字节', trigger: 'blur'}
  ],
  merchantAddress: [
    {max: 200, message: '客商地址长度不能超过200位字节', trigger: 'blur'}
  ],
  receivingBank: [
    {max: 80, message: '收款银行长度不能超过80位字节', trigger: 'blur'}
  ],
  receiverAccountNum: [
    {max: 80, message: '收款方帐号长度不能超过80位字节', trigger: 'blur'}
  ],
  note: [
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ],
  tradeCountry: [
    {max: 60, message: '贸易国别长度不能超过60位字节', trigger: 'blur'}
  ],
  shipper: [
    {max: 300, message: '装运人长度不能超过300位字节', trigger: 'blur'}
  ],
  consignee: [
    {max: 300, message: '收货人长度不能超过300位字节', trigger: 'blur'}
  ],
  notifyParty: [
    {max: 300, message: '通知人长度不能超过300位字节', trigger: 'blur'}
  ],
  warehouseAddress: [
    {max: 300, message: '仓储地址长度不能超过300位字节', trigger: 'blur'}
  ],
  contactPerson: [
    {max: 20, message: '联系人长度不能超过20位字节', trigger: 'blur'}
  ],
  contactPhone: [
    {max: 20, message: '联系电话长度不能超过20位字节', trigger: 'blur'}
  ]

}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData, {});
    // MerchantGetMerchantCodeClient({}).then((res)=>{
    //   if (res.code === 200){
    //     formData.merchantCode = res.data
    //   }
    // })
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});



// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        MerchantInsertClient(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        console.log('value',formData)
        MerchantUpdateClient(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

</script>

<style lang="less" scoped>


</style>




import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    // 'merchantCode',
    'financeCode',
    'merchantNameCn',
    'merchantNameEn',
    // 'merchantShort',
    'merchantType',
    'commonFlag',
    'commonFlagList',
    'fax',
    'email',
    'enAddress',
    'tradeCountryEn',
    'postcode',
    'bankAddress',
    'expendAccount',
    'taxNo',
    'legalPerson',
    'merchantAddress',
    'receivingBank',
    'receiverAccountNum',
    'note',
    'decPersonnelTel',
    'tradeCountry',
    'shipper',
    'consignee',
    'notifyParty',
    'warehouseAddress',
    'contactPerson',
    'contactPhone'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns,
    'merchantCode'
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable:"true",
    },
    {
      title: '客商编码',
      width: 150,
      align: 'center',
      dataIndex: 'merchantCode',
      key: 'merchantCode',
      resizable:"true",
    },
    {
      title: '财务系统编码',
      width: 150,
      align: 'center',
      dataIndex: 'financeCode',
      key: 'financeCode',
      resizable:"true",
    },
    {
      title: '客商中文名称',
      width: 220,
      align: 'center',
      dataIndex: 'merchantNameCn',
      key: 'merchantNameCn',
      resizable:"true",
    },
    {
      title: '客商英文名称',
      width: 220,
      align: 'center',
      dataIndex: 'merchantNameEn',
      key: 'merchantNameEn',
      resizable:"true",
    },
    {
      title: '客商类别',
      width: 150,
      align: 'center',
      dataIndex: 'merchantType',
      key: 'merchantType',
      resizable: "true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.merchantType))
      }
    },
    {
      title: '常用标识',
      width: 180,
      align: 'center',
      dataIndex: 'commonFlag',
      key: 'commonFlag',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }
    },
    // {
    //   title: '客商简称',
    //   width: 220,
    //   align: 'center',
    //   dataIndex: 'merchantShort',
    //   key: 'merchantShort',
    //   resizable:"true",
    // },
    {
      title: '客商地址',
      width: 150,
      align: 'center',
      dataIndex: 'merchantAddress',
      key: 'merchantAddress',
      resizable:"true",
    },
    {
      title: '收款银行',
      width: 150,
      align: 'center',
      dataIndex: 'receivingBank',
      key: 'receivingBank',
      resizable:"true",
    },
    {
      title: '收款方帐号',
      width: 150,
      align: 'center',
      dataIndex: 'receiverAccountNum',
      key: 'receiverAccountNum',
      resizable:"true",
    },
    {
      title: '备注',
      width: 150,
      align: 'center',
      dataIndex: 'note',
      key: 'note',
      resizable:"true",
    },
    {
      title: '贸易国别',
      width: 150,
      align: 'center',
      dataIndex: 'tradeCountry',
      key: 'tradeCountry',
      resizable:"true",
    },
    {
      title: '装运人SHIPPER',
      width: 200,
      align: 'center',
      dataIndex: 'shipper',
      key: 'shipper',
      resizable:"true",
    },
    {
      title: '收货人CONSIGNEE',
      width: 200,
      align: 'center',
      dataIndex: 'consignee',
      key: 'consignee',
      resizable:"true",
    },
    {
      title: '通知人NOTIFY PARTY',
      width: 200,
      align: 'center',
      dataIndex: 'notifyParty',
      key: 'notifyParty',
      resizable:"true",
    },
    {
      title: '仓储地址',
      width: 200,
      align: 'center',
      dataIndex: 'warehouseAddress',
      key: 'warehouseAddress',
      resizable:"true",
    },
    {
      title: '联系人',
      width: 120,
      align: 'center',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
      resizable:"true",
    },
    {
      title: '联系电话',
      width: 150,
      align: 'center',
      dataIndex: 'contactPhone',
      key: 'contactPhone',
      resizable:"true",
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns,
    commColumns
  }
}


